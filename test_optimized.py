#!/usr/bin/env python3
"""
测试优化后的 augment_automation.py
"""

import asyncio
from augment_automation import OptimizedAugmentAutomation

async def test_automation():
    """测试优化的自动化流程"""
    # 使用测试邮箱
    test_email = "<EMAIL>"  # 请替换为实际的测试邮箱
    
    print("🚀 开始测试优化的自动化流程")
    print(f"📧 测试邮箱: {test_email}")
    print("="*50)
    
    automation = OptimizedAugmentAutomation(test_email)
    
    try:
        # 执行所有步骤
        success = await automation.run_all_steps()
        
        if success:
            print("\n🎉 测试成功！所有步骤都已完成")
        else:
            print("\n❌ 测试失败！请检查日志")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
    finally:
        print("\n🧹 清理资源...")
        await automation.cleanup()
        print("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(test_automation())