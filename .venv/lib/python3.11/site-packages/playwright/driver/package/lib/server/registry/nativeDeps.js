"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var nativeDeps_exports = {};
__export(nativeDeps_exports, {
  deps: () => deps
});
module.exports = __toCommonJS(nativeDeps_exports);
const deps = {
  "ubuntu20.04-x64": {
    tools: [
      "xvfb",
      "fonts-noto-color-emoji",
      "ttf-unifont",
      "libfontconfig",
      "libfreetype6",
      "xfonts-cyrillic",
      "xfonts-scalable",
      "fonts-liberation",
      "fonts-ipafont-gothic",
      "fonts-wqy-zenhei",
      "fonts-tlwg-loma-otf",
      "ttf-ubuntu-font-family"
    ],
    chromium: [
      "fonts-liberation",
      "libasound2",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libatspi2.0-0",
      "libcairo2",
      "libcups2",
      "libdbus-1-3",
      "libdrm2",
      "libegl1",
      "libgbm1",
      "libglib2.0-0",
      "libgtk-3-0",
      "libnspr4",
      "libnss3",
      "libpango-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb1",
      "libxcomposite1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxrandr2",
      "libxshmfence1"
    ],
    firefox: [
      "ffmpeg",
      "libatk1.0-0",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libdbus-glib-1-2",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf2.0-0",
      "libglib2.0-0",
      "libgtk-3-0",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libpangoft2-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb-shm0",
      "libxcb1",
      "libxcomposite1",
      "libxcursor1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxi6",
      "libxrender1",
      "libxt6",
      "libxtst6"
    ],
    webkit: [
      "libenchant-2-2",
      "libflite1",
      "libx264-155",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libcairo2",
      "libegl1",
      "libenchant1c2a",
      "libepoxy0",
      "libevdev2",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf2.0-0",
      "libgl1",
      "libgles2",
      "libglib2.0-0",
      "libgtk-3-0",
      "libgudev-1.0-0",
      "libharfbuzz-icu0",
      "libharfbuzz0b",
      "libhyphen0",
      "libicu66",
      "libjpeg-turbo8",
      "libnghttp2-14",
      "libnotify4",
      "libopengl0",
      "libopenjp2-7",
      "libopus0",
      "libpango-1.0-0",
      "libpng16-16",
      "libsecret-1-0",
      "libvpx6",
      "libwayland-client0",
      "libwayland-egl1",
      "libwayland-server0",
      "libwebp6",
      "libwebpdemux2",
      "libwoff1",
      "libx11-6",
      "libxcomposite1",
      "libxdamage1",
      "libxkbcommon0",
      "libxml2",
      "libxslt1.1",
      "libatomic1",
      "libevent-2.1-7"
    ],
    lib2package: {
      "libflite.so.1": "libflite1",
      "libflite_usenglish.so.1": "libflite1",
      "libflite_cmu_grapheme_lang.so.1": "libflite1",
      "libflite_cmu_grapheme_lex.so.1": "libflite1",
      "libflite_cmu_indic_lang.so.1": "libflite1",
      "libflite_cmu_indic_lex.so.1": "libflite1",
      "libflite_cmulex.so.1": "libflite1",
      "libflite_cmu_time_awb.so.1": "libflite1",
      "libflite_cmu_us_awb.so.1": "libflite1",
      "libflite_cmu_us_kal16.so.1": "libflite1",
      "libflite_cmu_us_kal.so.1": "libflite1",
      "libflite_cmu_us_rms.so.1": "libflite1",
      "libflite_cmu_us_slt.so.1": "libflite1",
      "libx264.so": "libx264-155",
      "libasound.so.2": "libasound2",
      "libatk-1.0.so.0": "libatk1.0-0",
      "libatk-bridge-2.0.so.0": "libatk-bridge2.0-0",
      "libatspi.so.0": "libatspi2.0-0",
      "libcairo-gobject.so.2": "libcairo-gobject2",
      "libcairo.so.2": "libcairo2",
      "libcups.so.2": "libcups2",
      "libdbus-1.so.3": "libdbus-1-3",
      "libdbus-glib-1.so.2": "libdbus-glib-1-2",
      "libdrm.so.2": "libdrm2",
      "libEGL.so.1": "libegl1",
      "libenchant.so.1": "libenchant1c2a",
      "libevdev.so.2": "libevdev2",
      "libepoxy.so.0": "libepoxy0",
      "libfontconfig.so.1": "libfontconfig1",
      "libfreetype.so.6": "libfreetype6",
      "libgbm.so.1": "libgbm1",
      "libgdk_pixbuf-2.0.so.0": "libgdk-pixbuf2.0-0",
      "libgdk-3.so.0": "libgtk-3-0",
      "libgdk-x11-2.0.so.0": "libgtk2.0-0",
      "libgio-2.0.so.0": "libglib2.0-0",
      "libGL.so.1": "libgl1",
      "libGLESv2.so.2": "libgles2",
      "libglib-2.0.so.0": "libglib2.0-0",
      "libgmodule-2.0.so.0": "libglib2.0-0",
      "libgobject-2.0.so.0": "libglib2.0-0",
      "libgthread-2.0.so.0": "libglib2.0-0",
      "libgtk-3.so.0": "libgtk-3-0",
      "libgtk-x11-2.0.so.0": "libgtk2.0-0",
      "libgudev-1.0.so.0": "libgudev-1.0-0",
      "libharfbuzz-icu.so.0": "libharfbuzz-icu0",
      "libharfbuzz.so.0": "libharfbuzz0b",
      "libhyphen.so.0": "libhyphen0",
      "libicui18n.so.66": "libicu66",
      "libicuuc.so.66": "libicu66",
      "libjpeg.so.8": "libjpeg-turbo8",
      "libnotify.so.4": "libnotify4",
      "libnspr4.so": "libnspr4",
      "libnss3.so": "libnss3",
      "libnssutil3.so": "libnss3",
      "libOpenGL.so.0": "libopengl0",
      "libopenjp2.so.7": "libopenjp2-7",
      "libopus.so.0": "libopus0",
      "libpango-1.0.so.0": "libpango-1.0-0",
      "libpangocairo-1.0.so.0": "libpangocairo-1.0-0",
      "libpangoft2-1.0.so.0": "libpangoft2-1.0-0",
      "libpng16.so.16": "libpng16-16",
      "libsecret-1.so.0": "libsecret-1-0",
      "libsmime3.so": "libnss3",
      "libvpx.so.6": "libvpx6",
      "libwayland-client.so.0": "libwayland-client0",
      "libwayland-egl.so.1": "libwayland-egl1",
      "libwayland-server.so.0": "libwayland-server0",
      "libwebp.so.6": "libwebp6",
      "libwebpdemux.so.2": "libwebpdemux2",
      "libwoff2dec.so.1.0.2": "libwoff1",
      "libX11-xcb.so.1": "libx11-xcb1",
      "libX11.so.6": "libx11-6",
      "libxcb-dri3.so.0": "libxcb-dri3-0",
      "libxcb-shm.so.0": "libxcb-shm0",
      "libxcb.so.1": "libxcb1",
      "libXcomposite.so.1": "libxcomposite1",
      "libXcursor.so.1": "libxcursor1",
      "libXdamage.so.1": "libxdamage1",
      "libXext.so.6": "libxext6",
      "libXfixes.so.3": "libxfixes3",
      "libXi.so.6": "libxi6",
      "libxkbcommon.so.0": "libxkbcommon0",
      "libxml2.so.2": "libxml2",
      "libXrandr.so.2": "libxrandr2",
      "libXrender.so.1": "libxrender1",
      "libxslt.so.1": "libxslt1.1",
      "libXt.so.6": "libxt6",
      "libXtst.so.6": "libxtst6",
      "libxshmfence.so.1": "libxshmfence1",
      "libatomic.so.1": "libatomic1",
      "libenchant-2.so.2": "libenchant-2-2",
      "libevent-2.1.so.7": "libevent-2.1-7"
    }
  },
  "ubuntu22.04-x64": {
    tools: [
      "xvfb",
      "fonts-noto-color-emoji",
      "fonts-unifont",
      "libfontconfig1",
      "libfreetype6",
      "xfonts-cyrillic",
      "xfonts-scalable",
      "fonts-liberation",
      "fonts-ipafont-gothic",
      "fonts-wqy-zenhei",
      "fonts-tlwg-loma-otf",
      "fonts-freefont-ttf"
    ],
    chromium: [
      "libasound2",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libatspi2.0-0",
      "libcairo2",
      "libcups2",
      "libdbus-1-3",
      "libdrm2",
      "libgbm1",
      "libglib2.0-0",
      "libnspr4",
      "libnss3",
      "libpango-1.0-0",
      "libwayland-client0",
      "libx11-6",
      "libxcb1",
      "libxcomposite1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxkbcommon0",
      "libxrandr2"
    ],
    firefox: [
      "ffmpeg",
      "libasound2",
      "libatk1.0-0",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libdbus-glib-1-2",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf-2.0-0",
      "libglib2.0-0",
      "libgtk-3-0",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb-shm0",
      "libxcb1",
      "libxcomposite1",
      "libxcursor1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxi6",
      "libxrandr2",
      "libxrender1",
      "libxtst6"
    ],
    webkit: [
      "libsoup-3.0-0",
      "libenchant-2-2",
      "gstreamer1.0-libav",
      "gstreamer1.0-plugins-bad",
      "gstreamer1.0-plugins-base",
      "gstreamer1.0-plugins-good",
      "libicu70",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libcairo2",
      "libdbus-1-3",
      "libdrm2",
      "libegl1",
      "libepoxy0",
      "libevdev2",
      "libffi7",
      "libfontconfig1",
      "libfreetype6",
      "libgbm1",
      "libgdk-pixbuf-2.0-0",
      "libgles2",
      "libglib2.0-0",
      "libglx0",
      "libgstreamer-gl1.0-0",
      "libgstreamer-plugins-base1.0-0",
      "libgstreamer1.0-0",
      "libgtk-4-1",
      "libgudev-1.0-0",
      "libharfbuzz-icu0",
      "libharfbuzz0b",
      "libhyphen0",
      "libjpeg-turbo8",
      "liblcms2-2",
      "libmanette-0.2-0",
      "libnotify4",
      "libopengl0",
      "libopenjp2-7",
      "libopus0",
      "libpango-1.0-0",
      "libpng16-16",
      "libproxy1v5",
      "libsecret-1-0",
      "libwayland-client0",
      "libwayland-egl1",
      "libwayland-server0",
      "libwebpdemux2",
      "libwoff1",
      "libx11-6",
      "libxcomposite1",
      "libxdamage1",
      "libxkbcommon0",
      "libxml2",
      "libxslt1.1",
      "libx264-163",
      "libatomic1",
      "libevent-2.1-7",
      "libavif13"
    ],
    lib2package: {
      "libavif.so.13": "libavif13",
      "libsoup-3.0.so.0": "libsoup-3.0-0",
      "libasound.so.2": "libasound2",
      "libatk-1.0.so.0": "libatk1.0-0",
      "libatk-bridge-2.0.so.0": "libatk-bridge2.0-0",
      "libatspi.so.0": "libatspi2.0-0",
      "libcairo-gobject.so.2": "libcairo-gobject2",
      "libcairo.so.2": "libcairo2",
      "libcups.so.2": "libcups2",
      "libdbus-1.so.3": "libdbus-1-3",
      "libdbus-glib-1.so.2": "libdbus-glib-1-2",
      "libdrm.so.2": "libdrm2",
      "libEGL.so.1": "libegl1",
      "libepoxy.so.0": "libepoxy0",
      "libevdev.so.2": "libevdev2",
      "libffi.so.7": "libffi7",
      "libfontconfig.so.1": "libfontconfig1",
      "libfreetype.so.6": "libfreetype6",
      "libgbm.so.1": "libgbm1",
      "libgdk_pixbuf-2.0.so.0": "libgdk-pixbuf-2.0-0",
      "libgdk-3.so.0": "libgtk-3-0",
      "libgio-2.0.so.0": "libglib2.0-0",
      "libGLESv2.so.2": "libgles2",
      "libglib-2.0.so.0": "libglib2.0-0",
      "libGLX.so.0": "libglx0",
      "libgmodule-2.0.so.0": "libglib2.0-0",
      "libgobject-2.0.so.0": "libglib2.0-0",
      "libgstallocators-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstapp-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstaudio-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstbase-1.0.so.0": "libgstreamer1.0-0",
      "libgstfft-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstgl-1.0.so.0": "libgstreamer-gl1.0-0",
      "libgstpbutils-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstreamer-1.0.so.0": "libgstreamer1.0-0",
      "libgsttag-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstvideo-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgtk-3.so.0": "libgtk-3-0",
      "libgtk-4.so.1": "libgtk-4-1",
      "libgudev-1.0.so.0": "libgudev-1.0-0",
      "libharfbuzz-icu.so.0": "libharfbuzz-icu0",
      "libharfbuzz.so.0": "libharfbuzz0b",
      "libhyphen.so.0": "libhyphen0",
      "libjpeg.so.8": "libjpeg-turbo8",
      "liblcms2.so.2": "liblcms2-2",
      "libmanette-0.2.so.0": "libmanette-0.2-0",
      "libnotify.so.4": "libnotify4",
      "libnspr4.so": "libnspr4",
      "libnss3.so": "libnss3",
      "libnssutil3.so": "libnss3",
      "libOpenGL.so.0": "libopengl0",
      "libopenjp2.so.7": "libopenjp2-7",
      "libopus.so.0": "libopus0",
      "libpango-1.0.so.0": "libpango-1.0-0",
      "libpangocairo-1.0.so.0": "libpangocairo-1.0-0",
      "libpng16.so.16": "libpng16-16",
      "libproxy.so.1": "libproxy1v5",
      "libsecret-1.so.0": "libsecret-1-0",
      "libsmime3.so": "libnss3",
      "libwayland-client.so.0": "libwayland-client0",
      "libwayland-egl.so.1": "libwayland-egl1",
      "libwayland-server.so.0": "libwayland-server0",
      "libwebpdemux.so.2": "libwebpdemux2",
      "libwoff2dec.so.1.0.2": "libwoff1",
      "libX11-xcb.so.1": "libx11-xcb1",
      "libX11.so.6": "libx11-6",
      "libxcb-shm.so.0": "libxcb-shm0",
      "libxcb.so.1": "libxcb1",
      "libXcomposite.so.1": "libxcomposite1",
      "libXcursor.so.1": "libxcursor1",
      "libXdamage.so.1": "libxdamage1",
      "libXext.so.6": "libxext6",
      "libXfixes.so.3": "libxfixes3",
      "libXi.so.6": "libxi6",
      "libxkbcommon.so.0": "libxkbcommon0",
      "libxml2.so.2": "libxml2",
      "libXrandr.so.2": "libxrandr2",
      "libXrender.so.1": "libxrender1",
      "libxslt.so.1": "libxslt1.1",
      "libXtst.so.6": "libxtst6",
      "libicui18n.so.60": "libicu70",
      "libicuuc.so.66": "libicu70",
      "libicui18n.so.66": "libicu70",
      "libwebp.so.6": "libwebp6",
      "libenchant-2.so.2": "libenchant-2-2",
      "libx264.so": "libx264-163",
      "libvpx.so.7": "libvpx7",
      "libatomic.so.1": "libatomic1",
      "libevent-2.1.so.7": "libevent-2.1-7"
    }
  },
  "ubuntu24.04-x64": {
    tools: [
      "xvfb",
      "fonts-noto-color-emoji",
      "fonts-unifont",
      "libfontconfig1",
      "libfreetype6",
      "xfonts-cyrillic",
      "xfonts-scalable",
      "fonts-liberation",
      "fonts-ipafont-gothic",
      "fonts-wqy-zenhei",
      "fonts-tlwg-loma-otf",
      "fonts-freefont-ttf"
    ],
    chromium: [
      "libasound2t64",
      "libatk-bridge2.0-0t64",
      "libatk1.0-0t64",
      "libatspi2.0-0t64",
      "libcairo2",
      "libcups2t64",
      "libdbus-1-3",
      "libdrm2",
      "libgbm1",
      "libglib2.0-0t64",
      "libnspr4",
      "libnss3",
      "libpango-1.0-0",
      "libx11-6",
      "libxcb1",
      "libxcomposite1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxkbcommon0",
      "libxrandr2"
    ],
    firefox: [
      "libasound2t64",
      "libatk1.0-0t64",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf-2.0-0",
      "libglib2.0-0t64",
      "libgtk-3-0t64",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb-shm0",
      "libxcb1",
      "libxcomposite1",
      "libxcursor1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxi6",
      "libxrandr2",
      "libxrender1"
    ],
    webkit: [
      "gstreamer1.0-libav",
      "gstreamer1.0-plugins-bad",
      "gstreamer1.0-plugins-base",
      "gstreamer1.0-plugins-good",
      "libicu74",
      "libatomic1",
      "libatk-bridge2.0-0t64",
      "libatk1.0-0t64",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libdrm2",
      "libenchant-2-2",
      "libepoxy0",
      "libevent-2.1-7t64",
      "libflite1",
      "libfontconfig1",
      "libfreetype6",
      "libgbm1",
      "libgdk-pixbuf-2.0-0",
      "libgles2",
      "libglib2.0-0t64",
      "libgstreamer-gl1.0-0",
      "libgstreamer-plugins-bad1.0-0",
      "libgstreamer-plugins-base1.0-0",
      "libgstreamer1.0-0",
      "libgtk-4-1",
      "libharfbuzz-icu0",
      "libharfbuzz0b",
      "libhyphen0",
      "libicu74",
      "libjpeg-turbo8",
      "liblcms2-2",
      "libmanette-0.2-0",
      "libopus0",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libpng16-16t64",
      "libsecret-1-0",
      "libvpx9",
      "libwayland-client0",
      "libwayland-egl1",
      "libwayland-server0",
      "libwebp7",
      "libwebpdemux2",
      "libwoff1",
      "libx11-6",
      "libxkbcommon0",
      "libxml2",
      "libxslt1.1",
      "libx264-164",
      "libavif16"
    ],
    lib2package: {
      "libavif.so.16": "libavif16",
      "libasound.so.2": "libasound2t64",
      "libatk-1.0.so.0": "libatk1.0-0t64",
      "libatk-bridge-2.0.so.0": "libatk-bridge2.0-0t64",
      "libatomic.so.1": "libatomic1",
      "libatspi.so.0": "libatspi2.0-0t64",
      "libcairo-gobject.so.2": "libcairo-gobject2",
      "libcairo.so.2": "libcairo2",
      "libcups.so.2": "libcups2t64",
      "libdbus-1.so.3": "libdbus-1-3",
      "libdrm.so.2": "libdrm2",
      "libenchant-2.so.2": "libenchant-2-2",
      "libepoxy.so.0": "libepoxy0",
      "libevent-2.1.so.7": "libevent-2.1-7t64",
      "libflite_cmu_grapheme_lang.so.1": "libflite1",
      "libflite_cmu_grapheme_lex.so.1": "libflite1",
      "libflite_cmu_indic_lang.so.1": "libflite1",
      "libflite_cmu_indic_lex.so.1": "libflite1",
      "libflite_cmu_time_awb.so.1": "libflite1",
      "libflite_cmu_us_awb.so.1": "libflite1",
      "libflite_cmu_us_kal.so.1": "libflite1",
      "libflite_cmu_us_kal16.so.1": "libflite1",
      "libflite_cmu_us_rms.so.1": "libflite1",
      "libflite_cmu_us_slt.so.1": "libflite1",
      "libflite_cmulex.so.1": "libflite1",
      "libflite_usenglish.so.1": "libflite1",
      "libflite.so.1": "libflite1",
      "libfontconfig.so.1": "libfontconfig1",
      "libfreetype.so.6": "libfreetype6",
      "libgbm.so.1": "libgbm1",
      "libgdk_pixbuf-2.0.so.0": "libgdk-pixbuf-2.0-0",
      "libgdk-3.so.0": "libgtk-3-0t64",
      "libgio-2.0.so.0": "libglib2.0-0t64",
      "libGLESv2.so.2": "libgles2",
      "libglib-2.0.so.0": "libglib2.0-0t64",
      "libgmodule-2.0.so.0": "libglib2.0-0t64",
      "libgobject-2.0.so.0": "libglib2.0-0t64",
      "libgstallocators-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstapp-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstaudio-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstbase-1.0.so.0": "libgstreamer1.0-0",
      "libgstcodecparsers-1.0.so.0": "libgstreamer-plugins-bad1.0-0",
      "libgstfft-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstgl-1.0.so.0": "libgstreamer-gl1.0-0",
      "libgstpbutils-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstreamer-1.0.so.0": "libgstreamer1.0-0",
      "libgsttag-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstvideo-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgtk-3.so.0": "libgtk-3-0t64",
      "libgtk-4.so.1": "libgtk-4-1",
      "libharfbuzz-icu.so.0": "libharfbuzz-icu0",
      "libharfbuzz.so.0": "libharfbuzz0b",
      "libhyphen.so.0": "libhyphen0",
      "libicudata.so.74": "libicu74",
      "libicui18n.so.74": "libicu74",
      "libicuuc.so.74": "libicu74",
      "libjpeg.so.8": "libjpeg-turbo8",
      "liblcms2.so.2": "liblcms2-2",
      "libmanette-0.2.so.0": "libmanette-0.2-0",
      "libnspr4.so": "libnspr4",
      "libnss3.so": "libnss3",
      "libnssutil3.so": "libnss3",
      "libopus.so.0": "libopus0",
      "libpango-1.0.so.0": "libpango-1.0-0",
      "libpangocairo-1.0.so.0": "libpangocairo-1.0-0",
      "libpng16.so.16": "libpng16-16t64",
      "libsecret-1.so.0": "libsecret-1-0",
      "libsmime3.so": "libnss3",
      "libsoup-3.0.so.0": "libsoup-3.0-0",
      "libvpx.so.9": "libvpx9",
      "libwayland-client.so.0": "libwayland-client0",
      "libwayland-egl.so.1": "libwayland-egl1",
      "libwayland-server.so.0": "libwayland-server0",
      "libwebp.so.7": "libwebp7",
      "libwebpdemux.so.2": "libwebpdemux2",
      "libwoff2dec.so.1.0.2": "libwoff1",
      "libX11-xcb.so.1": "libx11-xcb1",
      "libX11.so.6": "libx11-6",
      "libxcb-shm.so.0": "libxcb-shm0",
      "libxcb.so.1": "libxcb1",
      "libXcomposite.so.1": "libxcomposite1",
      "libXcursor.so.1": "libxcursor1",
      "libXdamage.so.1": "libxdamage1",
      "libXext.so.6": "libxext6",
      "libXfixes.so.3": "libxfixes3",
      "libXi.so.6": "libxi6",
      "libxkbcommon.so.0": "libxkbcommon0",
      "libxml2.so.2": "libxml2",
      "libXrandr.so.2": "libxrandr2",
      "libXrender.so.1": "libxrender1",
      "libxslt.so.1": "libxslt1.1",
      "libx264.so": "libx264-164"
    }
  },
  "debian11-x64": {
    tools: [
      "xvfb",
      "fonts-noto-color-emoji",
      "fonts-unifont",
      "libfontconfig1",
      "libfreetype6",
      "xfonts-cyrillic",
      "xfonts-scalable",
      "fonts-liberation",
      "fonts-ipafont-gothic",
      "fonts-wqy-zenhei",
      "fonts-tlwg-loma-otf",
      "fonts-freefont-ttf"
    ],
    chromium: [
      "libasound2",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libatspi2.0-0",
      "libcairo2",
      "libcups2",
      "libdbus-1-3",
      "libdrm2",
      "libgbm1",
      "libglib2.0-0",
      "libnspr4",
      "libnss3",
      "libpango-1.0-0",
      "libwayland-client0",
      "libx11-6",
      "libxcb1",
      "libxcomposite1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxkbcommon0",
      "libxrandr2"
    ],
    firefox: [
      "libasound2",
      "libatk1.0-0",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libdbus-glib-1-2",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf-2.0-0",
      "libglib2.0-0",
      "libgtk-3-0",
      "libharfbuzz0b",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb-shm0",
      "libxcb1",
      "libxcomposite1",
      "libxcursor1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxi6",
      "libxrandr2",
      "libxrender1",
      "libxtst6"
    ],
    webkit: [
      "gstreamer1.0-libav",
      "gstreamer1.0-plugins-bad",
      "gstreamer1.0-plugins-base",
      "gstreamer1.0-plugins-good",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libcairo2",
      "libdbus-1-3",
      "libdrm2",
      "libegl1",
      "libenchant-2-2",
      "libepoxy0",
      "libevdev2",
      "libfontconfig1",
      "libfreetype6",
      "libgbm1",
      "libgdk-pixbuf-2.0-0",
      "libgles2",
      "libglib2.0-0",
      "libglx0",
      "libgstreamer-gl1.0-0",
      "libgstreamer-plugins-base1.0-0",
      "libgstreamer1.0-0",
      "libgtk-3-0",
      "libgudev-1.0-0",
      "libharfbuzz-icu0",
      "libharfbuzz0b",
      "libhyphen0",
      "libicu67",
      "libjpeg62-turbo",
      "liblcms2-2",
      "libmanette-0.2-0",
      "libnghttp2-14",
      "libnotify4",
      "libopengl0",
      "libopenjp2-7",
      "libopus0",
      "libpango-1.0-0",
      "libpng16-16",
      "libproxy1v5",
      "libsecret-1-0",
      "libwayland-client0",
      "libwayland-egl1",
      "libwayland-server0",
      "libwebp6",
      "libwebpdemux2",
      "libwoff1",
      "libx11-6",
      "libxcomposite1",
      "libxdamage1",
      "libxkbcommon0",
      "libxml2",
      "libxslt1.1",
      "libatomic1",
      "libevent-2.1-7"
    ],
    lib2package: {
      "libasound.so.2": "libasound2",
      "libatk-1.0.so.0": "libatk1.0-0",
      "libatk-bridge-2.0.so.0": "libatk-bridge2.0-0",
      "libatspi.so.0": "libatspi2.0-0",
      "libcairo-gobject.so.2": "libcairo-gobject2",
      "libcairo.so.2": "libcairo2",
      "libcups.so.2": "libcups2",
      "libdbus-1.so.3": "libdbus-1-3",
      "libdbus-glib-1.so.2": "libdbus-glib-1-2",
      "libdrm.so.2": "libdrm2",
      "libEGL.so.1": "libegl1",
      "libenchant-2.so.2": "libenchant-2-2",
      "libepoxy.so.0": "libepoxy0",
      "libevdev.so.2": "libevdev2",
      "libfontconfig.so.1": "libfontconfig1",
      "libfreetype.so.6": "libfreetype6",
      "libgbm.so.1": "libgbm1",
      "libgdk_pixbuf-2.0.so.0": "libgdk-pixbuf-2.0-0",
      "libgdk-3.so.0": "libgtk-3-0",
      "libgio-2.0.so.0": "libglib2.0-0",
      "libGLESv2.so.2": "libgles2",
      "libglib-2.0.so.0": "libglib2.0-0",
      "libGLX.so.0": "libglx0",
      "libgmodule-2.0.so.0": "libglib2.0-0",
      "libgobject-2.0.so.0": "libglib2.0-0",
      "libgstallocators-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstapp-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstaudio-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstbase-1.0.so.0": "libgstreamer1.0-0",
      "libgstfft-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstgl-1.0.so.0": "libgstreamer-gl1.0-0",
      "libgstpbutils-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstreamer-1.0.so.0": "libgstreamer1.0-0",
      "libgsttag-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgstvideo-1.0.so.0": "libgstreamer-plugins-base1.0-0",
      "libgtk-3.so.0": "libgtk-3-0",
      "libgudev-1.0.so.0": "libgudev-1.0-0",
      "libharfbuzz-icu.so.0": "libharfbuzz-icu0",
      "libharfbuzz.so.0": "libharfbuzz0b",
      "libhyphen.so.0": "libhyphen0",
      "libicui18n.so.67": "libicu67",
      "libicuuc.so.67": "libicu67",
      "libjpeg.so.62": "libjpeg62-turbo",
      "liblcms2.so.2": "liblcms2-2",
      "libmanette-0.2.so.0": "libmanette-0.2-0",
      "libnotify.so.4": "libnotify4",
      "libnspr4.so": "libnspr4",
      "libnss3.so": "libnss3",
      "libnssutil3.so": "libnss3",
      "libOpenGL.so.0": "libopengl0",
      "libopenjp2.so.7": "libopenjp2-7",
      "libopus.so.0": "libopus0",
      "libpango-1.0.so.0": "libpango-1.0-0",
      "libpangocairo-1.0.so.0": "libpangocairo-1.0-0",
      "libpng16.so.16": "libpng16-16",
      "libproxy.so.1": "libproxy1v5",
      "libsecret-1.so.0": "libsecret-1-0",
      "libsmime3.so": "libnss3",
      "libwayland-client.so.0": "libwayland-client0",
      "libwayland-egl.so.1": "libwayland-egl1",
      "libwayland-server.so.0": "libwayland-server0",
      "libwebp.so.6": "libwebp6",
      "libwebpdemux.so.2": "libwebpdemux2",
      "libwoff2dec.so.1.0.2": "libwoff1",
      "libX11-xcb.so.1": "libx11-xcb1",
      "libX11.so.6": "libx11-6",
      "libxcb-shm.so.0": "libxcb-shm0",
      "libxcb.so.1": "libxcb1",
      "libXcomposite.so.1": "libxcomposite1",
      "libXcursor.so.1": "libxcursor1",
      "libXdamage.so.1": "libxdamage1",
      "libXext.so.6": "libxext6",
      "libXfixes.so.3": "libxfixes3",
      "libXi.so.6": "libxi6",
      "libxkbcommon.so.0": "libxkbcommon0",
      "libxml2.so.2": "libxml2",
      "libXrandr.so.2": "libxrandr2",
      "libXrender.so.1": "libxrender1",
      "libxslt.so.1": "libxslt1.1",
      "libXtst.so.6": "libxtst6",
      "libatomic.so.1": "libatomic1",
      "libevent-2.1.so.7": "libevent-2.1-7"
    }
  },
  "debian12-x64": {
    tools: [
      "xvfb",
      "fonts-noto-color-emoji",
      "fonts-unifont",
      "libfontconfig1",
      "libfreetype6",
      "xfonts-scalable",
      "fonts-liberation",
      "fonts-ipafont-gothic",
      "fonts-wqy-zenhei",
      "fonts-tlwg-loma-otf",
      "fonts-freefont-ttf"
    ],
    chromium: [
      "libasound2",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libatspi2.0-0",
      "libcairo2",
      "libcups2",
      "libdbus-1-3",
      "libdrm2",
      "libgbm1",
      "libglib2.0-0",
      "libnspr4",
      "libnss3",
      "libpango-1.0-0",
      "libx11-6",
      "libxcb1",
      "libxcomposite1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxkbcommon0",
      "libxrandr2"
    ],
    firefox: [
      "libasound2",
      "libatk1.0-0",
      "libcairo-gobject2",
      "libcairo2",
      "libdbus-1-3",
      "libdbus-glib-1-2",
      "libfontconfig1",
      "libfreetype6",
      "libgdk-pixbuf-2.0-0",
      "libglib2.0-0",
      "libgtk-3-0",
      "libharfbuzz0b",
      "libpango-1.0-0",
      "libpangocairo-1.0-0",
      "libx11-6",
      "libx11-xcb1",
      "libxcb-shm0",
      "libxcb1",
      "libxcomposite1",
      "libxcursor1",
      "libxdamage1",
      "libxext6",
      "libxfixes3",
      "libxi6",
      "libxrandr2",
      "libxrender1",
      "libxtst6"
    ],
    webkit: [
      "libsoup-3.0-0",
      "gstreamer1.0-libav",
      "gstreamer1.0-plugins-bad",
      "gstreamer1.0-plugins-base",
      "gstreamer1.0-plugins-good",
      "libatk-bridge2.0-0",
      "libatk1.0-0",
      "libcairo2",
      "libdbus-1-3",
      "libdrm2",
      "libegl1",
      "libenchant-2-2",
      "libepoxy0",
      "libevdev2",
      "libfontconfig1",
      "libfreetype6",
      "libgbm1",
      "libgdk-pixbuf-2.0-0",
      "libgles2",
      "libglib2.0-0",
      "libglx0",
      "libgstreamer-gl1.0-0",
      "libgstreamer-plugins-base1.0-0",
      "libgstreamer1.0-0",
      "libgtk-4-1",
      "libgudev-1.0-0",
      "libharfbuzz-icu0",
      "libharfbuzz0b",
      "libhyphen0",
      "libicu72",
      "libjpeg62-turbo",
      "liblcms2-2",
      "libmanette-0.2-0",
      "libnotify4",
      "libopengl0",
      "libopenjp2-7",
      "libopus0",
      "libpango-1.0-0",
      "libpng16-16",
      "libproxy1v5",
      "libsecret-1-0",
      "libwayland-client0",
      "libwayland-egl1",
      "libwayland-server0",
      "libwebp7",
      "libwebpdemux2",
      "libwoff1",
      "libx11-6",
      "libxcomposite1",
      "libxdamage1",
      "libxkbcommon0",
      "libxml2",
      "libxslt1.1",
      "libatomic1",
      "libevent-2.1-7",
      "libavif15"
    ],
    lib2package: {
      "libavif.so.15": "libavif15",
      "libsoup-3.0.so.0": "libsoup-3.0-0",
      "libasound.so.2": "libasound2",
      "libatk-1.0.so.0": "libatk1.0-0",
      "libatk-bridge-2.0.so.0": "libatk-bridge2.0-0",
      "libatspi.so.0": "libatspi2.0-0",
      "libcairo.so.2": "libcairo2",
      "libcups.so.2": "libcups2",
      "libdbus-1.so.3": "libdbus-1-3",
      "libdrm.so.2": "libdrm2",
      "libgbm.so.1": "libgbm1",
      "libgio-2.0.so.0": "libglib2.0-0",
      "libglib-2.0.so.0": "libglib2.0-0",
      "libgobject-2.0.so.0": "libglib2.0-0",
      "libnspr4.so": "libnspr4",
      "libnss3.so": "libnss3",
      "libnssutil3.so": "libnss3",
      "libpango-1.0.so.0": "libpango-1.0-0",
      "libsmime3.so": "libnss3",
      "libX11.so.6": "libx11-6",
      "libxcb.so.1": "libxcb1",
      "libXcomposite.so.1": "libxcomposite1",
      "libXdamage.so.1": "libxdamage1",
      "libXext.so.6": "libxext6",
      "libXfixes.so.3": "libxfixes3",
      "libxkbcommon.so.0": "libxkbcommon0",
      "libXrandr.so.2": "libxrandr2",
      "libgtk-4.so.1": "libgtk-4-1"
    }
  }
};
deps["ubuntu20.04-arm64"] = {
  tools: [...deps["ubuntu20.04-x64"].tools],
  chromium: [...deps["ubuntu20.04-x64"].chromium],
  firefox: [
    ...deps["ubuntu20.04-x64"].firefox
  ],
  webkit: [
    ...deps["ubuntu20.04-x64"].webkit
  ],
  lib2package: {
    ...deps["ubuntu20.04-x64"].lib2package
  }
};
deps["ubuntu22.04-arm64"] = {
  tools: [...deps["ubuntu22.04-x64"].tools],
  chromium: [...deps["ubuntu22.04-x64"].chromium],
  firefox: [
    ...deps["ubuntu22.04-x64"].firefox
  ],
  webkit: [
    ...deps["ubuntu22.04-x64"].webkit
  ],
  lib2package: {
    ...deps["ubuntu22.04-x64"].lib2package
  }
};
deps["ubuntu24.04-arm64"] = {
  tools: [...deps["ubuntu24.04-x64"].tools],
  chromium: [...deps["ubuntu24.04-x64"].chromium],
  firefox: [
    ...deps["ubuntu24.04-x64"].firefox
  ],
  webkit: [
    ...deps["ubuntu24.04-x64"].webkit
  ],
  lib2package: {
    ...deps["ubuntu24.04-x64"].lib2package
  }
};
deps["debian11-arm64"] = {
  tools: [...deps["debian11-x64"].tools],
  chromium: [...deps["debian11-x64"].chromium],
  firefox: [
    ...deps["debian11-x64"].firefox
  ],
  webkit: [
    ...deps["debian11-x64"].webkit
  ],
  lib2package: {
    ...deps["debian11-x64"].lib2package
  }
};
deps["debian12-arm64"] = {
  tools: [...deps["debian12-x64"].tools],
  chromium: [...deps["debian12-x64"].chromium],
  firefox: [
    ...deps["debian12-x64"].firefox
  ],
  webkit: [
    ...deps["debian12-x64"].webkit
  ],
  lib2package: {
    ...deps["debian12-x64"].lib2package
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  deps
});
