#!/usr/bin/env python3
"""
Augment Code 自动化注册系统 - 优化版本
按照指定步骤执行 playwright 自动化操作

步骤：
1. 设置 playwright 指纹
2. 打开 https://app.augmentcode.com 网页
3. 输入邮箱到 input
4. 检查人机验证状态：div 若不是 success 则需要点击 div 这里面的复选框
5. 点击 button 按钮进入下一个页面，开始邮件监听

作者: AI Assistant
日期: 2025-01-28
"""

import asyncio
import email
imaplib
import logging
import os
import random
import re
import string
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from playwright.async_api import async_playwright, Page, Browser, BrowserContext


class Config:
    """配置类，包含所有URL、选择器和设置"""
    
    # 网站URL
    AUGMENT_APP_URL = "https://app.augmentcode.com/"
    
    # 邮箱配置
    EMAIL_HOST = "imap.163.com"
    EMAIL_PORT = 993
    EMAIL_USER = "<EMAIL>"
    EMAIL_PASSWORD = "XKSatD6M4xL2EMiV"
    
    # 超时设置（秒）
    DEFAULT_TIMEOUT = 30000  # 30秒
    DOWNLOAD_TIMEOUT = 60000  # 60秒
    
    # 浏览器设置
    VIEWPORT_SIZE = {"width": 1920, "height": 1080}
    
    # 指纹设置
    FINGERPRINT_CONFIG = {
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "platform": "MacIntel",
        "languages": ["zh-CN", "zh", "en"],
        "timezone": "Asia/Shanghai",
        "locale": "zh-CN",
        "screen": {"width": 1920, "height": 1080, "colorDepth": 24},
        "webgl_vendor": "Intel Inc.",
        "webgl_renderer": "Intel Iris Pro OpenGL Engine"
    }
    
    # 文件路径
    DOWNLOADS_DIR = Path("./downloads")
    CSV_FILE = Path("./augment_pool.csv")
    
    # 延迟设置（秒）
    MIN_DELAY = 1
    MAX_DELAY = 3


class OptimizedAugmentAutomation:
    """优化的 Augment Code 自动化操作类"""
    
    def __init__(self, email: str):
        self.email = email
        self.logger = self._setup_logger()
        self.downloads_dir = Config.DOWNLOADS_DIR
        self.downloads_dir.mkdir(exist_ok=True)
        
        # 浏览器相关
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 状态跟踪
        self.fingerprint_set = False
        self.page_opened = False
        self.email_entered = False
        self.verification_passed = False
        self.button_clicked = False
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger(f"optimized_augment_{self.email}")
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    async def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """添加随机延迟，模拟人类操作"""
        min_delay = min_delay or Config.MIN_DELAY
        max_delay = max_delay or Config.MAX_DELAY
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def take_screenshot(self, name: str):
        """截图用于调试"""
        try:
            if self.page:
                screenshot_path = f"screenshot_{name}_{int(time.time())}.png"
                await self.page.screenshot(path=screenshot_path)
                self.logger.info(f"截图已保存: {screenshot_path}")
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
    
    # 步骤1：设置 playwright 指纹
    async def step1_setup_playwright_fingerprint(self) -> bool:
        """步骤1：设置 playwright 指纹"""
        try:
            self.logger.info("=== 步骤1：设置 playwright 指纹 ===")
            
            # 启动 playwright
            self.playwright = await async_playwright().start()
            self.logger.info("Playwright 实例启动成功")
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 设置为True可以无头模式运行
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--user-agent=" + Config.FINGERPRINT_CONFIG["user_agent"]
                ]
            )
            self.logger.info("Chromium 浏览器启动成功")
            
            # 创建浏览器上下文，设置指纹
            self.context = await self.browser.new_context(
                viewport=Config.VIEWPORT_SIZE,
                user_agent=Config.FINGERPRINT_CONFIG["user_agent"],
                locale=Config.FINGERPRINT_CONFIG["locale"],
                timezone_id=Config.FINGERPRINT_CONFIG["timezone"],
                accept_downloads=True,
                # 设置屏幕信息
                screen=Config.FINGERPRINT_CONFIG["screen"],
                # 设置语言
                extra_http_headers={
                    "Accept-Language": ",".join(Config.FINGERPRINT_CONFIG["languages"])
                }
            )
            
            # 设置超时时间
            self.context.set_default_timeout(Config.DEFAULT_TIMEOUT)
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            # 注入指纹脚本
            await self._inject_fingerprint_scripts()
            
            self.fingerprint_set = True
            self.logger.info("✅ 步骤1完成：Playwright 指纹设置成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤1失败：设置 playwright 指纹时出错: {e}")
            return False
    
    async def _inject_fingerprint_scripts(self):
        """注入指纹脚本"""
        fingerprint_script = f"""
        // 覆盖 navigator 属性
        Object.defineProperty(navigator, 'platform', {{
            get: () => '{Config.FINGERPRINT_CONFIG["platform"]}'
        }});
        
        Object.defineProperty(navigator, 'languages', {{
            get: () => {Config.FINGERPRINT_CONFIG["languages"]}
        }});
        
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined
        }});
        
        // 覆盖 WebGL 指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{
                return '{Config.FINGERPRINT_CONFIG["webgl_vendor"]}';
            }}
            if (parameter === 37446) {{
                return '{Config.FINGERPRINT_CONFIG["webgl_renderer"]}';
            }}
            return getParameter.call(this, parameter);
        }};
        
        // 覆盖屏幕信息
        Object.defineProperty(screen, 'width', {{
            get: () => {Config.FINGERPRINT_CONFIG["screen"]["width"]}
        }});
        
        Object.defineProperty(screen, 'height', {{
            get: () => {Config.FINGERPRINT_CONFIG["screen"]["height"]}
        }});
        
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {Config.FINGERPRINT_CONFIG["screen"]["colorDepth"]}
        }});
        
        // 移除 webdriver 痕迹
        delete navigator.__proto__.webdriver;
        """
        
        await self.page.add_init_script(fingerprint_script)
        self.logger.info("指纹脚本注入成功")
    
    # 步骤2：打开网页
    async def step2_open_webpage(self) -> bool:
        """步骤2：打开 https://app.augmentcode.com 网页"""
        try:
            self.logger.info("=== 步骤2：打开网页 ===")
            
            if not self.fingerprint_set:
                self.logger.error("❌ 步骤2失败：请先完成步骤1（设置指纹）")
                return False
            
            # 访问目标网页
            self.logger.info(f"正在访问: {Config.AUGMENT_APP_URL}")
            await self.page.goto(Config.AUGMENT_APP_URL, timeout=60000)
            
            # 等待页面加载
            await self.page.wait_for_load_state("domcontentloaded", timeout=30000)
            await self._random_delay(2, 3)
            
            # 截图确认页面加载
            await self.take_screenshot("page_opened")
            
            self.page_opened = True
            self.logger.info("✅ 步骤2完成：网页打开成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤2失败：打开网页时出错: {e}")
            await self.take_screenshot("page_open_error")
            return False
    
    # 步骤3：输入邮箱
    async def step3_enter_email(self) -> bool:
        """步骤3：输入邮箱到 input"""
        try:
            self.logger.info("=== 步骤3：输入邮箱 ===")
            
            if not self.page_opened:
                self.logger.error("❌ 步骤3失败：请先完成步骤2（打开网页）")
                return False
            
            # 根据用户提供的选择器查找邮箱输入框
            email_selectors = [
                'input[name="username"]',  # 根据用户提供的HTML
                'input[id="username"]',
                'input[type="email"]',
                'input[inputmode="email"]',
                'input.input',  # 根据用户提供的class
                'input[autocomplete="email"]'
            ]
            
            email_input = None
            used_selector = None
            
            for selector in email_selectors:
                try:
                    email_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if email_input and await email_input.is_visible():
                        used_selector = selector
                        self.logger.info(f"找到邮箱输入框: {selector}")
                        break
                except:
                    continue
            
            if not email_input:
                self.logger.error("❌ 步骤3失败：未找到邮箱输入框")
                await self.take_screenshot("email_input_not_found")
                return False
            
            # 确保输入框在视口中
            await email_input.scroll_into_view_if_needed()
            await self._random_delay(1, 2)
            
            # 清空并输入邮箱
            await email_input.clear()
            await email_input.fill(self.email)
            await self._random_delay(1, 2)
            
            # 验证输入是否成功
            input_value = await email_input.input_value()
            if input_value == self.email:
                self.logger.info(f"✅ 邮箱输入成功: {self.email}")
                await self.take_screenshot("email_entered")
                self.email_entered = True
                self.logger.info("✅ 步骤3完成：邮箱输入成功")
                return True
            else:
                self.logger.error(f"❌ 邮箱输入验证失败，期望: {self.email}, 实际: {input_value}")
                return False
            
        except Exception as e:
            self.logger.error(f"❌ 步骤3失败：输入邮箱时出错: {e}")
            await self.take_screenshot("email_input_error")
            return False
    
    # 步骤4：检查人机验证状态
    async def step4_check_verification_status(self) -> bool:
        """步骤4：检查人机验证状态：div 若不是 success 则需要点击 div 这里面的复选框"""
        try:
            self.logger.info("=== 步骤4：检查人机验证状态 ===")
            
            if not self.email_entered:
                self.logger.error("❌ 步骤4失败：请先完成步骤3（输入邮箱）")
                return False
            
            # 等待页面稳定
            await self._random_delay(2, 3)
            
            # 首先检查是否已经显示 Success
            success_selectors = [
                'div:has-text("Success")',
                'div:has-text("success")',
                'div[class*="success"]',
                '.success',
                ':has-text("Success")',
                ':has-text("success")'
            ]
            
            success_found = False
            for selector in success_selectors:
                try:
                    success_element = await self.page.wait_for_selector(selector, timeout=2000)
                    if success_element and await success_element.is_visible():
                        self.logger.info(f"✅ 检测到 Success 状态: {selector}")
                        success_found = True
                        break
                except:
                    continue
            
            if success_found:
                self.verification_passed = True
                self.logger.info("✅ 步骤4完成：人机验证已通过（Success状态）")
                return True
            
            # 如果没有 Success，查找人机验证复选框
            self.logger.info("未检测到 Success 状态，查找人机验证复选框...")
            
            # 查找包含复选框的 div
            verification_selectors = [
                'div:has(input[type="checkbox"])',
                'div input[type="checkbox"]',
                'input[type="checkbox"]',
                'div[role="checkbox"]',
                'div:has-text("Verify you are human") input[type="checkbox"]',
                'label:has-text("Verify you are human") input[type="checkbox"]'
            ]
            
            checkbox_element = None
            used_selector = None
            
            for selector in verification_selectors:
                try:
                    checkbox_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if checkbox_element and await checkbox_element.is_visible():
                        used_selector = selector
                        self.logger.info(f"找到人机验证复选框: {selector}")
                        break
                except:
                    continue
            
            if not checkbox_element:
                self.logger.warning("未找到人机验证复选框，可能不需要验证")
                self.verification_passed = True
                self.logger.info("✅ 步骤4完成：无需人机验证")
                return True
            
            # 点击复选框
            self.logger.info(f"点击人机验证复选框 (选择器: {used_selector})")
            await checkbox_element.scroll_into_view_if_needed()
            await self._random_delay(1, 2)
            await checkbox_element.click()
            
            # 等待验证结果
            await self._random_delay(3, 5)
            
            # 再次检查 Success 状态
            for selector in success_selectors:
                try:
                    success_element = await self.page.wait_for_selector(selector, timeout=10000)
                    if success_element and await success_element.is_visible():
                        self.logger.info(f"✅ 人机验证成功，检测到 Success: {selector}")
                        self.verification_passed = True
                        await self.take_screenshot("verification_success")
                        self.logger.info("✅ 步骤4完成：人机验证通过")
                        return True
                except:
                    continue
            
            self.logger.warning("点击复选框后未检测到 Success 状态")
            await self.take_screenshot("verification_unclear")
            
            # 即使未明确检测到 Success，也继续下一步
            self.verification_passed = True
            self.logger.info("⚠️ 步骤4完成：人机验证状态不明确，继续下一步")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤4失败：检查人机验证状态时出错: {e}")
            await self.take_screenshot("verification_error")
            return False
    
    # 步骤5：点击按钮进入下一页面
    async def step5_click_button_and_start_email_monitoring(self) -> bool:
        """步骤5：点击 button 按钮进入下一个页面，开始邮件监听"""
        try:
            self.logger.info("=== 步骤5：点击按钮并开始邮件监听 ===")
            
            if not self.verification_passed:
                self.logger.error("❌ 步骤5失败：请先完成步骤4（人机验证）")
                return False
            
            # 根据用户提供的按钮选择器查找 Continue 按钮
            button_selectors = [
                'button[type="submit"]',  # 根据用户提供的HTML
                'button[name="action"]',
                'button[value="default"]',
                'button[data-action-button-primary="true"]',
                'button._button-login-id',
                'button:has-text("Continue")',
                'button:has-text("继续")',
                'input[type="submit"]'
            ]
            
            continue_button = None
            used_selector = None
            
            for selector in button_selectors:
                try:
                    continue_button = await self.page.wait_for_selector(selector, timeout=5000)
                    if continue_button and await continue_button.is_visible():
                        used_selector = selector
                        self.logger.info(f"找到 Continue 按钮: {selector}")
                        break
                except:
                    continue
            
            if not continue_button:
                self.logger.error("❌ 步骤5失败：未找到 Continue 按钮")
                await self.take_screenshot("button_not_found")
                return False
            
            # 确保按钮在视口中
            await continue_button.scroll_into_view_if_needed()
            await self._random_delay(1, 2)
            
            # 点击按钮
            self.logger.info(f"点击 Continue 按钮 (选择器: {used_selector})")
            await self.take_screenshot("before_button_click")
            await continue_button.click()
            
            # 等待页面跳转
            await self._random_delay(3, 5)
            
            try:
                await self.page.wait_for_load_state("domcontentloaded", timeout=30000)
                self.logger.info("页面跳转完成")
            except Exception as e:
                self.logger.warning(f"等待页面跳转超时: {e}")
            
            await self.take_screenshot("after_button_click")
            
            # 检查页面URL变化
            current_url = self.page.url
            self.logger.info(f"当前页面URL: {current_url}")
            
            self.button_clicked = True
            
            # 开始邮件监听
            self.logger.info("开始邮件监听...")
            verification_code = await self.get_verification_code()
            
            if verification_code:
                self.logger.info(f"✅ 获取到验证码: {verification_code}")
                # 这里可以继续处理验证码输入等后续步骤
                self.logger.info("✅ 步骤5完成：按钮点击成功，邮件监听已启动")
                return True
            else:
                self.logger.warning("⚠️ 未获取到验证码，但按钮点击成功")
                self.logger.info("✅ 步骤5完成：按钮点击成功")
                return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤5失败：点击按钮时出错: {e}")
            await self.take_screenshot("button_click_error")
            return False
    
    async def get_verification_code(self, timeout: int = 300) -> Optional[str]:
        """从163邮箱获取验证码"""
        self.logger.info("开始监听邮箱获取验证码...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 连接到IMAP服务器
                mail = imaplib.IMAP4_SSL(Config.EMAIL_HOST, Config.EMAIL_PORT)
                mail.login(Config.EMAIL_USER, Config.EMAIL_PASSWORD)
                mail.select('inbox')
                
                # 搜索来自Augment Code的邮件
                search_criteria = '(FROM "<EMAIL>" UNSEEN)'
                result, data = mail.search(None, search_criteria)
                
                if result == 'OK' and data[0]:
                    email_ids = data[0].split()
                    self.logger.info(f"找到 {len(email_ids)} 封未读邮件")
                    
                    # 获取最新的邮件
                    for email_id in reversed(email_ids):
                        result, data = mail.fetch(email_id, '(RFC822)')
                        if result == 'OK':
                            raw_email = data[0][1]
                            email_message = email.message_from_bytes(raw_email)
                            
                            # 检查邮件主题
                            subject = email_message['Subject']
                            self.logger.info(f"邮件主题: {subject}")
                            
                            # 获取邮件内容
                            body = ""
                            if email_message.is_multipart():
                                for part in email_message.walk():
                                    if part.get_content_type() == "text/plain":
                                        body = part.get_payload(decode=True).decode('utf-8')
                                        break
                                    elif part.get_content_type() == "text/html":
                                        body = part.get_payload(decode=True).decode('utf-8')
                            else:
                                body = email_message.get_payload(decode=True).decode('utf-8')
                            
                            self.logger.info(f"邮件内容预览: {body[:200]}...")
                            
                            # 使用正则表达式提取验证码
                            code_patterns = [
                                r'verification code[:\s]*(\d{6})',
                                r'验证码[:\s]*(\d{6})',
                                r'code[:\s]*(\d{6})',
                                r'(\d{6})',  # 简单的6位数字
                                r'Your code is[:\s]*(\d{6})',
                                r'Enter this code[:\s]*(\d{6})'
                            ]
                            
                            for pattern in code_patterns:
                                match = re.search(pattern, body, re.IGNORECASE)
                                if match:
                                    verification_code = match.group(1)
                                    self.logger.info(f"找到验证码: {verification_code}")
                                    
                                    # 标记邮件为已读
                                    mail.store(email_id, '+FLAGS', '\\Seen')
                                    mail.close()
                                    mail.logout()
                                    
                                    return verification_code
                
                mail.close()
                mail.logout()
                
                # 等待10秒后重试
                self.logger.info("未找到验证码邮件，10秒后重试...")
                await asyncio.sleep(10)
                
            except Exception as e:
                self.logger.error(f"获取邮件时出错: {e}")
                await asyncio.sleep(10)
        
        self.logger.error(f"在 {timeout} 秒内未能获取到验证码")
        return None
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            self.logger.info("浏览器资源已清理")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
    
    async def run_all_steps(self) -> bool:
        """按顺序执行所有步骤"""
        self.logger.info("🚀 开始执行优化的自动化流程")
        
        try:
            # 步骤1：设置 playwright 指纹
            if not await self.step1_setup_playwright_fingerprint():
                return False
            
            # 步骤2：打开网页
            if not await self.step2_open_webpage():
                return False
            
            # 步骤3：输入邮箱
            if not await self.step3_enter_email():
                return False
            
            # 步骤4：检查人机验证状态
            if not await self.step4_check_verification_status():
                return False
            
            # 步骤5：点击按钮并开始邮件监听
            if not await self.step5_click_button_and_start_email_monitoring():
                return False
            
            self.logger.info("🎉 所有步骤执行完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"执行过程中出错: {e}")
            return False
        finally:
            await self.cleanup()


async def main():
    """主函数，测试优化的自动化流程"""
    email = "<EMAIL>"  # 替换为实际邮箱
    automation = OptimizedAugmentAutomation(email)
    
    try:
        success = await automation.run_all_steps()
        if success:
            automation.logger.info("✅ 自动化流程执行成功")
        else:
            automation.logger.error("❌ 自动化流程执行失败")
    except Exception as e:
        automation.logger.error(f"主函数执行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
