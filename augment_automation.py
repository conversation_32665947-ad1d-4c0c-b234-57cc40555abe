#!/usr/bin/env python3
"""
Augment Code 自动化注册和升级系统
基于 playwright-stealth 实现反检测的浏览器自动化

作者: AI Assistant
日期: 2025-01-28
"""

import asyncio
import logging
import os
import random
import string
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from playwright_stealth import Stealth


class Config:
    """配置类，包含所有URL、选择器和设置"""
    
    # 网站URL
    AUGMENT_LOGIN_URL = "https://login.augmentcode.com/"
    WIPDF_URL = "https://wipdf.vercel.app/"
    AUGMENT_RESOURCES_URL = "https://www.augmentcode.com/resources/cursor"
    
    # 超时设置（秒）
    DEFAULT_TIMEOUT = 30000  # 30秒
    DOWNLOAD_TIMEOUT = 60000  # 60秒
    UPLOAD_TIMEOUT = 120000  # 120秒
    
    # 浏览器设置
    VIEWPORT_SIZE = {"width": 1920, "height": 1080}
    USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 文件路径
    DOWNLOADS_DIR = Path("./downloads")
    CSV_FILE = Path("./augment_pool.csv")
    
    # 延迟设置（秒）
    MIN_DELAY = 1
    MAX_DELAY = 3


class AugmentAutomation:
    """Augment Code 自动化操作主类"""
    
    def __init__(self, email: str):
        self.email = email
        self.password = self._generate_password()
        self.logger = self._setup_logger()
        self.downloads_dir = Config.DOWNLOADS_DIR
        self.downloads_dir.mkdir(exist_ok=True)
        
        # 浏览器相关
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 状态跟踪
        self.registration_success = False
        self.pdf_downloaded = False
        self.pdf_uploaded = False
        self.plan_upgraded = False
        self.verification_success = False
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger(f"augment_automation_{self.email}")
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _generate_password(self, length: int = 12) -> str:
        """生成随机密码"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(characters) for _ in range(length))
    
    async def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """添加随机延迟，模拟人类操作"""
        min_delay = min_delay or Config.MIN_DELAY
        max_delay = max_delay or Config.MAX_DELAY
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def setup_browser(self) -> bool:
        """设置带有stealth功能的浏览器"""
        try:
            self.logger.info("正在启动浏览器...")

            # 先不使用stealth，确保基础功能正常
            self.logger.info("启动playwright实例...")
            self.playwright = await async_playwright().start()
            self.logger.info("playwright实例启动成功")

            self.logger.info("启动chromium浏览器...")
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 设置为True可以无头模式运行
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                ]
            )
            self.logger.info("chromium浏览器启动成功")

            self.logger.info("创建浏览器上下文...")
            self.context = await self.browser.new_context(
                viewport=Config.VIEWPORT_SIZE,
                user_agent=Config.USER_AGENT,
                locale="zh-CN",
                timezone_id="Asia/Shanghai",
                accept_downloads=True
            )
            self.logger.info("浏览器上下文创建成功")

            # 设置超时时间
            self.logger.info("设置超时时间...")
            self.context.set_default_timeout(Config.DEFAULT_TIMEOUT)

            self.logger.info("创建新页面...")
            self.page = await self.context.new_page()
            self.logger.info("新页面创建成功")

            # 验证基础功能
            self.logger.info("验证WebDriver状态...")
            webdriver_status = await self.page.evaluate("navigator.webdriver")
            self.logger.info(f"WebDriver检测状态: {webdriver_status}")

            self.logger.info("浏览器设置成功")
            return True

        except Exception as e:
            import traceback
            self.logger.error(f"浏览器设置失败: {e}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            self.logger.info("浏览器资源已清理")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
    
    async def take_screenshot(self, name: str):
        """截图用于调试"""
        try:
            if self.page:
                screenshot_path = f"screenshot_{name}_{int(time.time())}.png"
                await self.page.screenshot(path=screenshot_path)
                self.logger.info(f"截图已保存: {screenshot_path}")
        except Exception as e:
            self.logger.error(f"截图失败: {e}")

    async def register_account(self) -> bool:
        """在 login.augmentcode.com 注册账户"""
        try:
            self.logger.info(f"开始注册账户: {self.email}")

            # 访问注册页面
            await self.page.goto(Config.AUGMENT_LOGIN_URL)
            await self._random_delay()
            await self.take_screenshot("login_page")

            # 等待页面加载
            await self.page.wait_for_load_state("networkidle")

            # 分析页面结构
            self.logger.info("分析页面结构...")
            page_title = await self.page.title()
            self.logger.info(f"页面标题: {page_title}")

            # 获取页面的主要内容
            try:
                # 查找所有输入框
                inputs = await self.page.query_selector_all('input')
                self.logger.info(f"找到 {len(inputs)} 个输入框")

                for i, input_elem in enumerate(inputs):
                    input_type = await input_elem.get_attribute('type') or 'text'
                    input_name = await input_elem.get_attribute('name') or ''
                    input_placeholder = await input_elem.get_attribute('placeholder') or ''
                    input_id = await input_elem.get_attribute('id') or ''
                    self.logger.info(f"输入框 {i+1}: type={input_type}, name={input_name}, placeholder={input_placeholder}, id={input_id}")

                # 查找所有按钮
                buttons = await self.page.query_selector_all('button')
                self.logger.info(f"找到 {len(buttons)} 个按钮")

                for i, button in enumerate(buttons):
                    button_text = await button.text_content() or ''
                    button_type = await button.get_attribute('type') or ''
                    self.logger.info(f"按钮 {i+1}: text='{button_text}', type={button_type}")

            except Exception as e:
                self.logger.error(f"分析页面结构时出错: {e}")

            # 查找注册相关的元素
            # 这里需要根据实际页面结构来调整选择器
            self.logger.info("查找注册表单...")

            # 首先尝试找到注册按钮并点击
            signup_button_selectors = [
                'button:has-text("Sign up")',
                'button:has-text("注册")',
                'a:has-text("Sign up")',
                'a:has-text("注册")',
                'button:has-text("Install nowSign up")',
                '[href*="signup"]',
                '[href*="register"]'
            ]

            signup_button = None
            for selector in signup_button_selectors:
                try:
                    signup_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if signup_button:
                        self.logger.info(f"找到注册按钮: {selector}")
                        break
                except:
                    continue

            if signup_button:
                self.logger.info("点击注册按钮...")
                await signup_button.click()
                await self._random_delay(2, 4)
                await self.page.wait_for_load_state("networkidle")
                await self.take_screenshot("after_signup_click")

                # 重新分析页面结构
                page_title = await self.page.title()
                self.logger.info(f"点击后页面标题: {page_title}")

            # 尝试查找邮箱输入框
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="邮箱" i]',
                '#email',
                '.email'
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if email_input:
                        self.logger.info(f"找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                self.logger.error("未找到邮箱输入框")
                # 再次分析页面结构
                try:
                    inputs = await self.page.query_selector_all('input')
                    self.logger.info(f"当前页面有 {len(inputs)} 个输入框")
                    for i, input_elem in enumerate(inputs):
                        input_type = await input_elem.get_attribute('type') or 'text'
                        input_name = await input_elem.get_attribute('name') or ''
                        input_placeholder = await input_elem.get_attribute('placeholder') or ''
                        input_id = await input_elem.get_attribute('id') or ''
                        self.logger.info(f"输入框 {i+1}: type={input_type}, name={input_name}, placeholder={input_placeholder}, id={input_id}")
                except Exception as e:
                    self.logger.error(f"分析输入框时出错: {e}")
                return False

            # 输入邮箱
            await email_input.fill(self.email)
            await self._random_delay()

            # 查找密码输入框
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="password" i]',
                'input[placeholder*="密码" i]',
                '#password',
                '.password'
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if password_input:
                        self.logger.info(f"找到密码输入框: {selector}")
                        break
                except:
                    continue

            if password_input:
                await password_input.fill(self.password)
                await self._random_delay()

            # 查找注册按钮
            register_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("注册")',
                'button:has-text("Register")',
                'button:has-text("Sign up")',
                'button:has-text("创建账户")',
                '.register-btn',
                '.signup-btn'
            ]

            register_button = None
            for selector in register_selectors:
                try:
                    register_button = await self.page.wait_for_selector(selector, timeout=5000)
                    if register_button:
                        self.logger.info(f"找到注册按钮: {selector}")
                        break
                except:
                    continue

            if not register_button:
                self.logger.error("未找到注册按钮")
                return False

            # 点击注册按钮
            await self.take_screenshot("before_register")
            await register_button.click()
            await self._random_delay(2, 5)

            # 等待注册结果
            await self.page.wait_for_load_state("networkidle")
            await self.take_screenshot("after_register")

            # 检查是否注册成功
            # 这里需要根据实际页面的成功/失败提示来判断
            current_url = self.page.url
            self.logger.info(f"注册后页面URL: {current_url}")

            # 简单的成功判断逻辑，可能需要根据实际情况调整
            if "dashboard" in current_url.lower() or "welcome" in current_url.lower():
                self.logger.info("注册成功")
                self.registration_success = True
                return True
            else:
                # 检查是否有错误信息
                error_selectors = [
                    '.error',
                    '.alert-danger',
                    '.error-message',
                    '[class*="error"]'
                ]

                for selector in error_selectors:
                    try:
                        error_element = await self.page.wait_for_selector(selector, timeout=2000)
                        if error_element:
                            error_text = await error_element.text_content()
                            self.logger.warning(f"发现错误信息: {error_text}")
                    except:
                        continue

                self.logger.warning("注册状态不明确，需要人工检查")
                return False

        except Exception as e:
            import traceback
            self.logger.error(f"注册过程中出错: {e}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            await self.take_screenshot("register_error")
            return False


async def main():
    """主函数，测试注册功能"""
    email = "<EMAIL>"
    automation = AugmentAutomation(email)

    try:
        # 设置浏览器
        if await automation.setup_browser():
            automation.logger.info("浏览器设置成功")

            # 测试注册功能
            if await automation.register_account():
                automation.logger.info("账户注册成功")
            else:
                automation.logger.error("账户注册失败")

        else:
            automation.logger.error("浏览器设置失败")

    except Exception as e:
        automation.logger.error(f"执行过程中出错: {e}")
    finally:
        await automation.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
