#!/usr/bin/env python3
"""
Augment Code 自动化注册和升级系统
基于 playwright-stealth 实现反检测的浏览器自动化

作者: AI Assistant
日期: 2025-01-28
"""

import asyncio
import email
import imaplib
import logging
import os
import random
import re
import string
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from playwright_stealth import Stealth


class Config:
    """配置类，包含所有URL、选择器和设置"""
    
    # 网站URL
    AUGMENT_APP_URL = "https://app.augmentcode.com/"
    WIPDF_URL = "https://wipdf.vercel.app/"
    AUGMENT_RESOURCES_URL = "https://www.augmentcode.com/resources/cursor"

    # 邮箱配置
    EMAIL_HOST = "imap.163.com"
    EMAIL_PORT = 993
    EMAIL_USER = "<EMAIL>"
    EMAIL_PASSWORD = "XKSatD6M4xL2EMiV"
    
    # 超时设置（秒）
    DEFAULT_TIMEOUT = 30000  # 30秒
    DOWNLOAD_TIMEOUT = 60000  # 60秒
    UPLOAD_TIMEOUT = 120000  # 120秒
    
    # 浏览器设置
    VIEWPORT_SIZE = {"width": 1920, "height": 1080}
    USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 文件路径
    DOWNLOADS_DIR = Path("./downloads")
    CSV_FILE = Path("./augment_pool.csv")
    
    # 延迟设置（秒）
    MIN_DELAY = 1
    MAX_DELAY = 3


class AugmentAutomation:
    """Augment Code 自动化操作主类"""
    
    def __init__(self, email: str):
        self.email = email
        self.password = self._generate_password()
        self.logger = self._setup_logger()
        self.downloads_dir = Config.DOWNLOADS_DIR
        self.downloads_dir.mkdir(exist_ok=True)
        
        # 浏览器相关
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 状态跟踪
        self.registration_success = False
        self.pdf_downloaded = False
        self.pdf_uploaded = False
        self.plan_upgraded = False
        self.verification_success = False
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录"""
        logger = logging.getLogger(f"augment_automation_{self.email}")
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _generate_password(self, length: int = 12) -> str:
        """生成随机密码"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(characters) for _ in range(length))
    
    async def _random_delay(self, min_delay: float = None, max_delay: float = None):
        """添加随机延迟，模拟人类操作"""
        min_delay = min_delay or Config.MIN_DELAY
        max_delay = max_delay or Config.MAX_DELAY
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def setup_browser(self) -> bool:
        """设置带有stealth功能的浏览器"""
        try:
            self.logger.info("正在启动浏览器...")

            # 先不使用stealth，确保基础功能正常
            self.logger.info("启动playwright实例...")
            self.playwright = await async_playwright().start()
            self.logger.info("playwright实例启动成功")

            self.logger.info("启动chromium浏览器...")
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 设置为True可以无头模式运行
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                ]
            )
            self.logger.info("chromium浏览器启动成功")

            self.logger.info("创建浏览器上下文...")
            self.context = await self.browser.new_context(
                viewport=Config.VIEWPORT_SIZE,
                user_agent=Config.USER_AGENT,
                locale="zh-CN",
                timezone_id="Asia/Shanghai",
                accept_downloads=True
            )
            self.logger.info("浏览器上下文创建成功")

            # 设置超时时间
            self.logger.info("设置超时时间...")
            self.context.set_default_timeout(Config.DEFAULT_TIMEOUT)

            self.logger.info("创建新页面...")
            self.page = await self.context.new_page()
            self.logger.info("新页面创建成功")

            # 验证基础功能
            self.logger.info("验证WebDriver状态...")
            webdriver_status = await self.page.evaluate("navigator.webdriver")
            self.logger.info(f"WebDriver检测状态: {webdriver_status}")

            self.logger.info("浏览器设置成功")
            return True

        except Exception as e:
            import traceback
            self.logger.error(f"浏览器设置失败: {e}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            self.logger.info("浏览器资源已清理")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
    
    async def take_screenshot(self, name: str):
        """截图用于调试"""
        try:
            if self.page:
                screenshot_path = f"screenshot_{name}_{int(time.time())}.png"
                await self.page.screenshot(path=screenshot_path)
                self.logger.info(f"截图已保存: {screenshot_path}")
        except Exception as e:
            self.logger.error(f"截图失败: {e}")

    async def get_verification_code(self, timeout: int = 300) -> Optional[str]:
        """从163邮箱获取验证码"""
        self.logger.info("开始监听邮箱获取验证码...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 连接到IMAP服务器
                mail = imaplib.IMAP4_SSL(Config.EMAIL_HOST, Config.EMAIL_PORT)
                mail.login(Config.EMAIL_USER, Config.EMAIL_PASSWORD)
                mail.select('inbox')

                # 搜索来自Augment Code的邮件
                search_criteria = '(FROM "<EMAIL>" UNSEEN)'
                result, data = mail.search(None, search_criteria)

                if result == 'OK' and data[0]:
                    email_ids = data[0].split()
                    self.logger.info(f"找到 {len(email_ids)} 封未读邮件")

                    # 获取最新的邮件
                    for email_id in reversed(email_ids):
                        result, data = mail.fetch(email_id, '(RFC822)')
                        if result == 'OK':
                            raw_email = data[0][1]
                            email_message = email.message_from_bytes(raw_email)

                            # 检查邮件主题
                            subject = email_message['Subject']
                            self.logger.info(f"邮件主题: {subject}")

                            # 获取邮件内容
                            body = ""
                            if email_message.is_multipart():
                                for part in email_message.walk():
                                    if part.get_content_type() == "text/plain":
                                        body = part.get_payload(decode=True).decode('utf-8')
                                        break
                                    elif part.get_content_type() == "text/html":
                                        body = part.get_payload(decode=True).decode('utf-8')
                            else:
                                body = email_message.get_payload(decode=True).decode('utf-8')

                            self.logger.info(f"邮件内容预览: {body[:200]}...")

                            # 使用正则表达式提取验证码
                            # 常见的验证码格式：6位数字
                            code_patterns = [
                                r'verification code[:\s]*(\d{6})',
                                r'验证码[:\s]*(\d{6})',
                                r'code[:\s]*(\d{6})',
                                r'(\d{6})',  # 简单的6位数字
                                r'Your code is[:\s]*(\d{6})',
                                r'Enter this code[:\s]*(\d{6})'
                            ]

                            for pattern in code_patterns:
                                match = re.search(pattern, body, re.IGNORECASE)
                                if match:
                                    verification_code = match.group(1)
                                    self.logger.info(f"找到验证码: {verification_code}")

                                    # 标记邮件为已读
                                    mail.store(email_id, '+FLAGS', '\\Seen')
                                    mail.close()
                                    mail.logout()

                                    return verification_code

                mail.close()
                mail.logout()

                # 等待10秒后重试
                self.logger.info("未找到验证码邮件，10秒后重试...")
                await asyncio.sleep(10)

            except Exception as e:
                self.logger.error(f"获取邮件时出错: {e}")
                await asyncio.sleep(10)

        self.logger.error(f"在 {timeout} 秒内未能获取到验证码")
        return None

    async def register_account(self) -> bool:
        """在 app.augmentcode.com 注册账户"""
        try:
            self.logger.info(f"开始注册账户: {self.email}")

            # 访问注册页面
            await self.page.goto(Config.AUGMENT_APP_URL, timeout=60000)
            await self._random_delay(3, 5)  # 增加等待时间

            # 等待页面完全加载，使用更宽松的超时设置
            try:
                await self.page.wait_for_load_state("domcontentloaded", timeout=30000)
                self.logger.info("DOM内容已加载")
            except Exception as e:
                self.logger.warning(f"等待DOM加载超时: {e}")

            # 额外等待JavaScript渲染
            await asyncio.sleep(5)

            await self.take_screenshot("app_page")

            # 分析页面结构
            self.logger.info("分析页面结构...")
            page_title = await self.page.title()
            self.logger.info(f"页面标题: {page_title}")

            # 获取页面HTML内容用于调试
            try:
                page_content = await self.page.content()
                self.logger.info(f"页面内容长度: {len(page_content)} 字符")
                # 检查是否包含关键词
                if "Email address" in page_content:
                    self.logger.info("页面包含 'Email address' 文本")
                if "Continue with Google" in page_content:
                    self.logger.info("页面包含 'Continue with Google' 文本")
            except Exception as e:
                self.logger.error(f"获取页面内容失败: {e}")

            # 查找邮箱输入框
            self.logger.info("查找邮箱输入框...")
            email_selectors = [
                'input[placeholder="Email address*"]',  # 精确匹配
                'input[placeholder*="Email address" i]',
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="邮箱" i]',
                '#email',
                '.email',
                'input'  # 最后尝试所有input
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if email_input:
                        self.logger.info(f"找到邮箱输入框: {selector}")
                        break
                except:
                    continue

            if not email_input:
                self.logger.error("未找到邮箱输入框")
                # 分析页面结构
                try:
                    inputs = await self.page.query_selector_all('input')
                    self.logger.info(f"当前页面有 {len(inputs)} 个输入框")
                    for i, input_elem in enumerate(inputs):
                        input_type = await input_elem.get_attribute('type') or 'text'
                        input_name = await input_elem.get_attribute('name') or ''
                        input_placeholder = await input_elem.get_attribute('placeholder') or ''
                        input_id = await input_elem.get_attribute('id') or ''
                        self.logger.info(f"输入框 {i+1}: type={input_type}, name={input_name}, placeholder={input_placeholder}, id={input_id}")
                except Exception as e:
                    self.logger.error(f"分析输入框时出错: {e}")
                return False

            # 输入邮箱
            self.logger.info(f"输入邮箱: {self.email}")
            await email_input.fill(self.email)
            await self._random_delay()
            await self.take_screenshot("email_filled")

            # 查找并点击人机验证复选框
            self.logger.info("查找人机验证复选框...")
            verify_selectors = [
                'input[type="checkbox"]',
                'input:has-text("Verify you are human")',
                '[role="checkbox"]',
                'label:has-text("Verify you are human")',
                'text=Verify you are human'
            ]

            verify_checkbox = None
            for selector in verify_selectors:
                try:
                    verify_checkbox = await self.page.wait_for_selector(selector, timeout=5000)
                    if verify_checkbox:
                        self.logger.info(f"找到人机验证复选框: {selector}")
                        break
                except:
                    continue

            if verify_checkbox:
                self.logger.info("点击人机验证复选框...")
                await verify_checkbox.click()
                await self._random_delay(2, 3)
                await self.take_screenshot("verify_clicked")
            else:
                self.logger.warning("未找到人机验证复选框，继续执行...")

            # 检查是否显示 "Success!"
            self.logger.info("检查是否显示 Success...")
            success_selectors = [
                ':has-text("Success!")',
                '.success',
                '[class*="success"]',
                'text=Success!'
            ]

            success_found = False
            for selector in success_selectors:
                try:
                    success_element = await self.page.wait_for_selector(selector, timeout=3000)
                    if success_element:
                        self.logger.info(f"找到Success提示: {selector}")
                        success_found = True
                        break
                except:
                    continue

            if not success_found:
                self.logger.warning("未找到Success提示，继续尝试...")

            await self.take_screenshot("success_check")

            # 查找并点击 Continue 按钮
            self.logger.info("查找Continue按钮...")
            continue_selectors = [
                'button:has-text("Continue")',
                'button:has-text("继续")',
                'input[value="Continue"]',
                '[type="submit"]',
                'button[type="submit"]'
            ]

            continue_button = None
            for selector in continue_selectors:
                try:
                    continue_button = await self.page.wait_for_selector(selector, timeout=5000)
                    if continue_button:
                        self.logger.info(f"找到Continue按钮: {selector}")
                        break
                except:
                    continue

            if not continue_button:
                self.logger.error("未找到Continue按钮")
                return False

            # 点击Continue按钮
            self.logger.info("点击Continue按钮...")
            await self.take_screenshot("before_continue")
            await continue_button.click()
            await self._random_delay(2, 5)

            # 等待页面跳转
            await self.page.wait_for_load_state("networkidle")
            await self.take_screenshot("after_continue")

            current_url = self.page.url
            self.logger.info(f"点击Continue后页面URL: {current_url}")

            # 获取验证码
            verification_code = await self.get_verification_code()
            if not verification_code:
                self.logger.error("未能获取验证码")
                return False

            # 查找验证码输入框
            self.logger.info("查找验证码输入框...")
            code_selectors = [
                'input[placeholder*="code" i]',
                'input[placeholder*="验证码" i]',
                'input[name*="code" i]',
                'input[type="text"]',
                'input[type="number"]'
            ]

            code_input = None
            for selector in code_selectors:
                try:
                    code_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if code_input:
                        self.logger.info(f"找到验证码输入框: {selector}")
                        break
                except:
                    continue

            if not code_input:
                self.logger.error("未找到验证码输入框")
                return False

            # 输入验证码
            self.logger.info(f"输入验证码: {verification_code}")
            await code_input.fill(verification_code)
            await self._random_delay()
            await self.take_screenshot("code_filled")

            # 查找提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Submit")',
                'button:has-text("提交")',
                'button:has-text("Verify")',
                'button:has-text("验证")'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.wait_for_selector(selector, timeout=5000)
                    if submit_button:
                        self.logger.info(f"找到提交按钮: {selector}")
                        break
                except:
                    continue

            if submit_button:
                await submit_button.click()
                await self._random_delay(2, 5)
                await self.page.wait_for_load_state("networkidle")
                await self.take_screenshot("after_submit")

            # 检查注册是否成功
            final_url = self.page.url
            self.logger.info(f"最终页面URL: {final_url}")

            if "dashboard" in final_url.lower() or "app" in final_url.lower():
                self.logger.info("注册成功")
                self.registration_success = True
                return True
            else:
                self.logger.warning("注册状态不明确，需要人工检查")
                return False

        except Exception as e:
            import traceback
            self.logger.error(f"注册过程中出错: {e}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            await self.take_screenshot("register_error")
            return False


async def main():
    """主函数，测试注册功能"""
    email = "<EMAIL>"
    automation = AugmentAutomation(email)

    try:
        # 设置浏览器
        if await automation.setup_browser():
            automation.logger.info("浏览器设置成功")

            # 测试注册功能
            if await automation.register_account():
                automation.logger.info("账户注册成功")
            else:
                automation.logger.error("账户注册失败")

        else:
            automation.logger.error("浏览器设置失败")

    except Exception as e:
        automation.logger.error(f"执行过程中出错: {e}")
    finally:
        await automation.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
