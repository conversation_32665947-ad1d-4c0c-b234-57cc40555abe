# Augment Code 自动化系统 - 优化版本

这是一个基于 Playwright 的自动化脚本，用于在 Augment Code 网站上执行自动化操作。

## 功能特点

✨ **优化的5步流程**：
1. 🔧 设置 playwright 指纹
2. 🌐 打开 https://app.augmentcode.com 网页
3. 📧 输入邮箱到 input 输入框
4. 🤖 检查人机验证状态：若不是 success 则点击复选框
5. 🔘 点击 button 按钮进入下一页面，开始邮件监听

## 安装依赖

### 1. 创建虚拟环境
```bash
uv venv -p 3.12
source .venv/bin/activate
```

### 2. 配置代理（可选，用于更快下载）
```bash
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"
```

### 3. 安装依赖
```bash
uv install -r requirements.txt
```

### 4. 安装 Playwright 浏览器
```bash
playwright install chromium
```

## 使用方法

### 方法1：直接运行主脚本
```bash
python augment_automation.py
```

### 方法2：运行测试脚本
```bash
python test_optimized.py
```

### 方法3：在代码中使用
```python
import asyncio
from augment_automation import OptimizedAugmentAutomation

async def main():
    email = "<EMAIL>"
    automation = OptimizedAugmentAutomation(email)
    
    try:
        success = await automation.run_all_steps()
        if success:
            print("✅ 自动化流程执行成功")
        else:
            print("❌ 自动化流程执行失败")
    except Exception as e:
        print(f"执行出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

## 配置说明

### 邮箱配置
在 `Config` 类中修改邮箱设置：
```python
class Config:
    EMAIL_HOST = "imap.163.com"
    EMAIL_PORT = 993
    EMAIL_USER = "<EMAIL>"
    EMAIL_PASSWORD = "your_password"
```

### 指纹配置
可以在 `FINGERPRINT_CONFIG` 中自定义浏览器指纹：
```python
FINGERPRINT_CONFIG = {
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)...",
    "platform": "MacIntel",
    "languages": ["zh-CN", "zh", "en"],
    "timezone": "Asia/Shanghai",
    "locale": "zh-CN",
    "screen": {"width": 1920, "height": 1080, "colorDepth": 24},
    "webgl_vendor": "Intel Inc.",
    "webgl_renderer": "Intel Iris Pro OpenGL Engine"
}
```

## 步骤详解

### 步骤1：设置 Playwright 指纹
- 启动 Playwright 实例
- 配置浏览器参数
- 注入指纹脚本，隐藏自动化痕迹
- 设置用户代理、屏幕信息、WebGL 指纹等

### 步骤2：打开网页
- 访问 https://app.augmentcode.com
- 等待页面完全加载
- 截图确认页面状态

### 步骤3：输入邮箱
- 查找邮箱输入框（支持多种选择器）
- 清空并输入指定邮箱
- 验证输入是否成功

### 步骤4：检查人机验证状态
- 首先检查是否已显示 "Success" 状态
- 如果没有，查找人机验证复选框
- 点击复选框完成验证
- 等待验证结果

### 步骤5：点击按钮并开始邮件监听
- 查找 Continue 按钮
- 点击按钮进入下一页面
- 开始监听邮箱获取验证码
- 处理验证码（可扩展）

## 调试功能

- 📸 **自动截图**：每个关键步骤都会自动截图
- 📝 **详细日志**：完整的执行日志记录
- 🔍 **错误处理**：友好的错误提示和处理

## 注意事项

1. **邮箱配置**：确保邮箱账号和密码正确
2. **网络环境**：确保能正常访问目标网站
3. **浏览器版本**：建议使用最新版本的 Chromium
4. **代理设置**：如果使用代理，确保代理服务正常

## 故障排除

### 常见问题

1. **找不到元素**
   - 检查网页是否正常加载
   - 查看截图确认页面状态
   - 可能需要调整选择器

2. **人机验证失败**
   - 检查网络连接
   - 尝试手动完成一次验证
   - 可能需要更换IP或浏览器指纹

3. **邮件监听失败**
   - 检查邮箱配置
   - 确认邮箱服务正常
   - 检查防火墙设置

### 调试模式

设置 `headless=False` 可以看到浏览器操作过程：
```python
self.browser = await self.playwright.chromium.launch(
    headless=False,  # 显示浏览器窗口
    # ...
)
```

## 更新日志

### v2.0.0 (优化版本)
- ✨ 重新设计为5步流程
- 🔧 增强指纹设置功能
- 📝 改进日志和错误处理
- 🎯 更精确的元素选择器
- 📸 自动截图调试功能

## 许可证

本项目仅供学习和研究使用。